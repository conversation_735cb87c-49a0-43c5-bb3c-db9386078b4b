@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure full viewport usage without scrolling */
html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Mobile: Allow scrolling */
@media (max-width: 767px) {
  html,
  body {
    overflow: auto;
  }
}

/* Tablet and Desktop: Prevent scrolling */
@media (min-width: 768px) {
  html,
  body {
    overflow: hidden;
  }
}

/* Large tablets: Set max height */
@media (min-width: 768px) and (min-height: 900px) {
  html,
  body {
    max-height: 100vh;
  }
}

.scrollbar-hide::-webkit-scrollbar {
  width: 0; /* Hilangkan scrollbar secara visual */
  height: 0;
}

/* Browser Modern */
.scrollbar-hide {
  scrollbar-width: none; /* Hilangkan scrollbar untuk Firefox */
  -ms-overflow-style: none; /* Hilangkan scrollbar untuk Edge lama */
}
