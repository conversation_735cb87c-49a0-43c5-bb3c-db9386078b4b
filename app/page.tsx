"use client";

import AboutMe from "@/components/AboutMe";
import Blog from "@/components/Blog";
import Experience from "@/components/Experience";
import Footer from "@/components/Footer";
import GetInTouch from "@/components/GetInTouch";
import Intro from "@/components/Intro";
import Project from "@/components/Project";
import Skills from "@/components/Skills";

export default function Component() {
  return (
    <div className="h-screen w-screen bg-transparent p-2 md:p-4 flex items-center justify-center overflow-hidden">
      <div className="w-full h-full max-w-7xl grid grid-cols-1 md:grid-cols-4 gap-2 md:gap-4 grid-rows-[2fr_1fr_1fr] md:grid-rows-[2fr_2fr_1fr]">
        {/* Row 1: Intro takes 3 cols, AboutMe takes 1 col */}
        <div className="md:col-span-3 md:row-span-1">
          <Intro />
        </div>
        <div className="md:col-span-1 md:row-span-2">
          <AboutMe />
        </div>

        {/* Row 2: Blog, Skills, Project */}
        <div className="md:col-span-1 md:row-span-1">
          <Blog />
        </div>
        <div className="md:col-span-1 md:row-span-1">
          <Skills />
        </div>
        <div className="md:col-span-1 md:row-span-2">
          <Project />
        </div>

        {/* Row 3: Experience, GetInTouch, Footer */}
        <div className="md:col-span-1 md:row-span-1">
          <Experience />
        </div>
        <div className="md:col-span-1 md:row-span-1">
          <GetInTouch />
        </div>
        <div className="md:col-span-1 md:row-span-1">
          <Footer />
        </div>
      </div>
    </div>
  );
}
