"use client";

import AboutMe from "@/components/AboutMe";
import Blog from "@/components/Blog";
import Experience from "@/components/Experience";
import Footer from "@/components/Footer";
import GetInTouch from "@/components/GetInTouch";
import Intro from "@/components/Intro";
import Project from "@/components/Project";
import Skills from "@/components/Skills";

export default function Component() {
  return (
    <div className="min-h-screen w-screen bg-transparent p-2 md:p-4 flex items-center justify-center overflow-y-auto md:overflow-hidden lg:max-h-screen">
      <div className="w-full h-auto md:h-full max-w-7xl grid grid-cols-1 md:grid-cols-4 gap-2 md:gap-4 auto-rows-fr md:grid-rows-[3fr_3fr_2fr_2fr]">
        {/* Row 1: Intro takes 3 cols, AboutMe takes 1 col */}
        <div className="md:col-span-3 md:row-span-1">
          <Intro />
        </div>
        <div className="md:col-span-1 md:row-span-4">
          <AboutMe />
        </div>

        {/* Row 2: Blog, Skills, Project */}
        <div className="md:col-span-1 md:row-span-4">
          <Blog />
        </div>
        <div className="md:col-span-1 md:row-span-2">
          <Skills />
        </div>
        <div className="md:col-span-1 md:row-span-3">
          <Project />
        </div>

        {/* Row 3: Experience */}
        <div className="md:col-span-1 md:row-span-2">
          <Experience />
        </div>

        {/* Row 4: GetInTouch, Footer */}
        <div className="md:col-span-1 md:row-span-1">
          <GetInTouch />
        </div>
        <div className="md:col-span-1 md:row-span-1">
          <Footer />
        </div>
      </div>
    </div>
  );
}
