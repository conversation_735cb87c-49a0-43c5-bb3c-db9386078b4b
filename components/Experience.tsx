"use client";
import React, { createElement } from "react";
import { IconUserStar } from "@tabler/icons-react";
export default function Experience() {
  const experiences = [
    {
      title: "Frontend Developer",
      institution: "Paramadina University, Jakarta",
      period: "Auguts 2022 - Present",
      icon: IconUserStar,
    },
    {
      title: "Web Developer",
      institution: "PT. Petra Sejahtera Abadi, Tangerang",
      period: "September 2021 - June 2023",
      icon: IconUserStar,
    },
  ];

  return (
    <div className="h-full w-full bg-white dark:bg-zinc-900 p-2 md:p-3 text-white border border-zinc-400 hover:border-blue-500 rounded-lg flex flex-col">
      <h6 className="text-sm font-light text-black mb-2 dark:text-white">
        Experience
      </h6>
      <div className="flex flex-col h-full space-y-2 overflow-y-auto scrollbar-hide">
        {experiences.map((exp, index) => (
          <div key={index} className="relative pl-6">
            {/* Timeline line */}
            {index !== experiences.length - 1 && (
              <div className="absolute left-3 top-6 w-0.5 h-6 bg-gray-300 dark:bg-gray-700 -translate-x-1/2" />
            )}

            {/* Timeline dot and icon */}
            <div className="absolute left-0 top-0.5 w-4 h-4 rounded-full bg-white dark:bg-zinc-800 border border-blue-400 dark:border-blue-600 flex items-center justify-center">
              {createElement(exp.icon, {
                size: 8,
                className: "text-black dark:text-white",
              })}
            </div>

            {/* Content */}
            <div className="ml-2">
              <h3 className="font-bold text-black dark:text-white text-xs">
                {exp.title}
              </h3>
              <p className="text-[10px] text-zinc-800 dark:text-gray-100 font-light leading-tight">
                {exp.institution}
              </p>
              <p className="text-[10px] text-zinc-500 dark:text-gray-500">
                {exp.period}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
