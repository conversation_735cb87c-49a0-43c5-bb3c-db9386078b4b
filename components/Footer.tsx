"use client";
import React from "react";
import { IconMoon, IconSun } from "@tabler/icons-react";
import { useThemeContext } from "@/context/themeContext";

export default function Footer() {
  const { theme, setTheme } = useThemeContext();

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <div className="h-full w-full bg-white dark:bg-zinc-900 p-2 md:p-3 text-white border border-zinc-400 hover:border-blue-500 rounded-lg flex flex-col items-center justify-center gap-1">
      {/* Theme Toggle Button */}
      <button
        className="p-1 bg-blue-600/20 hover:bg-blue-600/30 text-blue-500 rounded-md transition-colors"
        onClick={toggleTheme}
        aria-label="Toggle theme"
      >
        {theme === "light" ? (
          <IconSun className="h-3 w-3" />
        ) : (
          <IconMoon className="h-3 w-3" />
        )}
      </button>

      {/* Footer Text */}
      <p className="text-center text-black dark:text-white text-[10px] leading-tight">
        &copy; {new Date().getFullYear()} | Crafted with ❤️ by Sodiq Ardianto.
      </p>
    </div>
  );
}
