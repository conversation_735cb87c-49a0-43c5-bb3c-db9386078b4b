"use client";
import React from "react";
import { IconMoon, IconSun } from "@tabler/icons-react";
import { useThemeContext } from "@/context/themeContext";

export default function Footer() {
  const { theme, setTheme } = useThemeContext();

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <div className="h-full w-full bg-white dark:bg-zinc-900 p-4 md:p-6 text-white border border-zinc-400 hover:border-blue-500 rounded-lg flex flex-col items-center justify-center gap-3">
      {/* Theme Toggle Button */}
      <button
        className="p-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-500 rounded-lg transition-colors"
        onClick={toggleTheme}
        aria-label="Toggle theme"
      >
        {theme === "light" ? (
          <IconSun className="h-4 w-4" />
        ) : (
          <IconMoon className="h-4 w-4" />
        )}
      </button>
      
      {/* Footer Text */}
      <p className="text-center text-black dark:text-white text-xs">
        &copy; {new Date().getFullYear()} | Crafted with ❤️ by Sodiq Ardianto.
      </p>
    </div>
  );
}
