"use client";
import React from "react";
import { IconMail } from "@tabler/icons-react";

export default function GetInTouch() {
  const handleClick = () => {
    window.open("mailto:<EMAIL>", "_blank");
  };

  return (
    <div className="h-full w-full bg-white dark:bg-zinc-900 p-4 md:p-6 text-white border border-zinc-400 hover:border-blue-500 rounded-lg flex flex-col justify-center">
      <div className="flex flex-col">
        <button
          className="relative inline-flex h-10 overflow-hidden rounded-full p-[2px] focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50 group"
          onClick={handleClick}
        >
          <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#E2CBFF_0%,#393BB2_50%,#E2CBFF_100%)]" />
          <span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-slate-950 px-3 py-1 text-xs font-medium text-white backdrop-blur-3xl overflow-hidden relative">
            {/* Text Border Magic */}
            <span className="absolute left-1/2 transform -translate-x-1/2 transition-transform duration-300 group-hover:translate-x-[200%]">
              Get in touch
            </span>

            {/* Icon Mail */}
            <span className="absolute left-[-50px] opacity-0 transform -translate-x-full transition-all duration-300 group-hover:left-1/2 group-hover:-translate-x-1/2 group-hover:opacity-100">
              <IconMail className="h-4 w-4 flex justify-center items-center" />
            </span>
          </span>
        </button>
      </div>
    </div>
  );
}
