"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { IconExternalLink } from "@tabler/icons-react";
import { limit45Alfhabet } from "@/app/utils/limit45Alfhabet";
import { ProjectInterface } from "@/interfaces/projects.interface";

export default function Project() {
  const projects: ProjectInterface[] = [];

  return (
    <div className="h-full w-full bg-white dark:bg-zinc-900 p-4 md:p-6 dark:text-white border border-zinc-400 hover:border-blue-500 rounded-lg flex flex-col">
      <div className="flex justify-between items-center mb-3">
        <h6 className="text-lg font-light text-black dark:text-white">
          Project
        </h6>
        <Link href="/projects">
          <IconExternalLink className="h-4 w-4 inline dark:text-white text-black hover:text-blue-500" />
        </Link>
      </div>
      <div className="flex flex-col h-full overflow-hidden">
        {projects.length === 0 ? (
          <p className="text-center text-neutral-600 dark:text-neutral-400 text-sm">
            No project available
          </p>
        ) : (
          <ul className="w-full gap-1 overflow-y-auto scrollbar-hide">
            {projects.map((project, index) => (
              <div
                key={index}
                className="p-2 flex items-center hover:bg-neutral-50 dark:hover:bg-neutral-800 rounded-lg cursor-pointer mb-2"
              >
                <div className="flex gap-2 items-center w-full">
                  <div className="flex-shrink-0">
                    <Image
                      width={40}
                      height={40}
                      src={project.image}
                      alt={project.title}
                      className="h-10 w-10 rounded-lg object-cover object-top"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-black dark:text-neutral-200 text-xs truncate">
                      {limit45Alfhabet(project.title)}
                    </h3>
                  </div>
                </div>
              </div>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
