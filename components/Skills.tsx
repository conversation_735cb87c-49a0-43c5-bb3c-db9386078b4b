import React from "react";
import { LinkPreview } from "./ui/link-preview";
import {
  IconBrandGit,
  IconBrandLaravel,
  IconBrandMongodb,
  IconBrandMysql,
  IconBrandNextjs,
  IconBrandNodejs,
  IconBrandReact,
  IconBrandTypescript,
} from "@tabler/icons-react";

export default function Skills() {
  const techStack = [
    { name: "React", url: "https://react.dev/", icon: IconBrandReact },
    { name: "Next.js", url: "https://nextjs.org/", icon: IconBrandNextjs },
    {
      name: "TypeScript",
      url: "https://www.typescriptlang.org/",
      icon: IconBrandTypescript,
    },
    {
      name: "Express.js",
      url: "https://expressjs.com/",
      icon: IconBrandNodejs,
    },
    { name: "Laravel", url: "https://laravel.com/", icon: IconBrandLaravel },
    {
      name: "MongoDB",
      url: "https://www.mongodb.com/",
      icon: IconBrandMongodb,
    },
    { name: "MySQL", url: "https://www.mysql.com/", icon: IconBrandMysql },
    { name: "Git", url: "https://git-scm.com/", icon: IconBrandGit },
  ];

  return (
    <div className="h-full w-full bg-white dark:bg-zinc-900 p-4 md:p-6 border border-zinc-400 hover:border-blue-500 rounded-lg flex flex-col">
      <h6 className="text-lg font-light text-black mb-3 dark:text-white">
        Skills
      </h6>
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <ul className="list-inside">
          <div className="flex justify-around">
            <div className="space-y-1">
              {techStack.slice(0, 4).map((tech, index) => (
                <li key={index} className="py-0.5">
                  {tech.url ? (
                    <LinkPreview
                      url={tech.url}
                      className="font-bold hover:text-blue-500 text-xs"
                    >
                      {tech.icon && (
                        <tech.icon className="inline-block w-3 h-3" />
                      )}{" "}
                      {tech.name}
                    </LinkPreview>
                  ) : (
                    <span className="text-xs">{tech.name}</span>
                  )}
                </li>
              ))}
            </div>
            <div className="space-y-1">
              {techStack.slice(4).map((tech, index) => (
                <li key={index + 4} className="py-0.5">
                  {tech.url ? (
                    <LinkPreview
                      url={tech.url}
                      className="font-bold hover:text-blue-500 text-xs"
                    >
                      {tech.icon && (
                        <tech.icon className="inline-block w-3 h-3" />
                      )}{" "}
                      {tech.name}
                    </LinkPreview>
                  ) : (
                    <span className="text-xs">{tech.name}</span>
                  )}
                </li>
              ))}
            </div>
          </div>
        </ul>
      </div>
    </div>
  );
}
